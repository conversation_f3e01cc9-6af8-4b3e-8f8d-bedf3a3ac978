import json
import tkinter as tk
from tkinter import messagebox, scrolledtext
import requests
import os
from datetime import datetime
from urllib.parse import quote
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, timedelta
from PIL import Image, ImageTk
import io
import threading
import queue
import time
import configparser
import logging
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import hashes, padding


def validate_key():
    """
    验证 key.vdf 文件的有效性

    检查授权文件是否存在并验证其有效期
    如果验证失败则退出程序
    """
    try:
        key_file = "key.vdf"
        if not os.path.exists(key_file):
            messagebox.showerror("错误", f"未找到授权文件 {key_file}")
            exit(1)

        # 密钥生成配置（必须与加密时一致）
        password = b'my_super_secret_password'
        salt = b'fixed_salt_value'
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = kdf.derive(password)

        # 读取加密文件
        with open(key_file, "rb") as f:
            data = f.read()
            if len(data) < 16:
                messagebox.showerror("错误", "授权文件格式错误")
                exit(1)
            iv = data[:16]  # 前16字节为IV
            ciphertext = data[16:]  # 剩余部分为密文

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        try:
            padded_data = decryptor.update(ciphertext) + decryptor.finalize()
        except ValueError as e:
            messagebox.showerror("错误", f"解密失败：数据可能被篡改 ({str(e)})")
            exit(1)

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        try:
            current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()
        except ValueError as e:
            messagebox.showerror("错误", f"数据校验失败：填充错误 ({str(e)})")
            exit(1)

        # 验证时间有效性
        try:
            stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))
        except ValueError as e:
            messagebox.showerror("错误", f"时间格式无效 ({str(e)})")
            exit(1)

        # 计算时间差
        time_difference = datetime.now() - stored_time

        # 有效期验证（30天）
        if time_difference > timedelta(days=30):
            messagebox.showerror("错误", "软件授权已过期")
            exit(1)
        else:
            # 创建一个临时的日志记录器用于验证成功的消息
            temp_logger = logging.getLogger('验证')
            temp_logger.setLevel(logging.INFO)
            if not temp_logger.handlers:
                handler = logging.FileHandler("操作日志.log", encoding='utf-8')
                formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
                handler.setFormatter(formatter)
                temp_logger.addHandler(handler)
            temp_logger.info("验证通过，欢迎使用本软件")

    except Exception as e:
        messagebox.showerror("错误", f"验证过程中发生未知错误: {str(e)}")
        exit(1)


class LogManager:
    """日志管理器"""

    def __init__(self, log_file="操作日志.log"):
        self.log_file = log_file
        self.setup_logger()

    def setup_logger(self):
        """设置日志记录器"""
        # 创建日志记录器
        self.logger = logging.getLogger('商品数据提取工具')
        self.logger.setLevel(logging.INFO)

        # 清除已有的处理器
        self.logger.handlers.clear()

        # 创建文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)

        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)

        # 添加处理器到记录器
        self.logger.addHandler(file_handler)

    def log_info(self, message):
        """记录信息日志"""
        self.logger.info(message)

    def log_warning(self, message):
        """记录警告日志"""
        self.logger.warning(message)

    def log_error(self, message):
        """记录错误日志"""
        self.logger.error(message)

    def log_success(self, message):
        """记录成功日志"""
        self.logger.info(f"[成功] {message}")


class ConfigManager:
    """配置文件管理器"""

    def __init__(self, config_file="配置.ini"):
        self.config_file = config_file
        self.config = configparser.ConfigParser()
        self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                self.config.read(self.config_file, encoding='utf-8')
            else:
                self.create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            self.create_default_config()

    def create_default_config(self):
        """创建默认配置"""
        self.config['路径配置'] = {
            '搜索基础路径': r'E:\图片\原图',
            '目标路径后缀': r'\导出图\已完成',
            '启用目标路径后缀': 'True'
        }
        self.config['文件配置'] = {
            '支持的图片格式': '.png,.jpg,.jpeg'
        }
        self.config['界面配置'] = {
            '窗口标题': '商品数据提取工具',
            '窗口大小': '1000x750',
            '最小窗口大小': '800x600'
        }
        self.config['下载配置'] = {
            '下载超时时间': '15',
            '最大重试次数': '3',
            '重试间隔': '2'
        }
        self.config['搜索配置'] = {
            '严格搜索路径限制': 'True',
            'Everything_API地址': 'http://localhost:8080/'
        }
        self.config['样式配置'] = {
            '字体名称': '微软雅黑',
            '主背景色': '#F5F7FA',
            '次背景色': '#FFFFFF',
            '主文字色': '#2D3748',
            '次文字色': '#4A5568',
            '主色调蓝': '#4299E1',
            '成功色绿': '#48BB78',
            '警告色橙': '#ED8936',
            '禁用色灰': '#A0AEC0',
            '控制台背景色': '#FFFFFF',
            '边框色': '#E2E8F0'
        }
        self.save_config()

    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get(self, section, key, fallback=None):
        """获取配置值"""
        try:
            return self.config.get(section, key, fallback=fallback)
        except:
            return fallback

    def set(self, section, key, value):
        """设置配置值"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, key, str(value))
        self.save_config()

    def get_bool(self, section, key, fallback=False):
        """获取布尔配置值"""
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except:
            return fallback

    def get_int(self, section, key, fallback=0):
        """获取整数配置值"""
        try:
            return self.config.getint(section, key, fallback=fallback)
        except:
            return fallback


class ImageSelectionDialog:
    """处理重复图片选择的对话框"""

    def __init__(self, parent, sku_or_product_id, image_files, callback, api_image_url=None, style_config=None):
        """
        初始化图片选择对话框

        Args:
            parent: 父窗口
            sku_or_product_id: SKU或产品ID
            image_files: 图片文件列表，格式为 [(item, file_path), ...]
            callback: 选择完成后的回调函数，接收选择的索引
            api_image_url: API商品主图的URL（如果有）
            style_config: 样式配置对象
        """
        self.parent = parent
        self.sku_or_product_id = sku_or_product_id
        self.image_files = image_files
        self.callback = callback
        self.selected_index = None
        self.api_image_url = api_image_url
        self.api_image_path = None
        self.style_config = style_config
        
        # 创建对话框
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(f"选择图片 - {sku_or_product_id}")
        self.dialog.geometry("1200x800")  # 增加窗口大小
        self.dialog.minsize(1200, 800)  # 增加最小窗口大小
        self.dialog.transient(parent)  # 设置为父窗口的临时窗口
        self.dialog.grab_set()  # 模态对话框
        
        self.setup_ui()
        self.load_images()
        
    def setup_ui(self):
        """设置UI界面"""
        # 主容器
        self.main_frame = tk.Frame(self.dialog, bg=self.style_config.COLOR_SCHEME['primary_bg'])
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)  # 增加内边距

        # 标题标签
        self.title_label = tk.Label(
            self.main_frame,
            text=f"发现多张图片匹配 {self.sku_or_product_id}，请选择要保留的图片",
            font=self.style_config.FONT_SETTINGS['subtitle'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.title_label.pack(fill=tk.X, pady=(0, 15))

        # 说明标签
        self.info_label = tk.Label(
            self.main_frame,
            text="右键点击要保留的图片，或使用下方按钮选择",
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['accent_orange'],
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.info_label.pack(fill=tk.X, pady=(0, 15))

        # 创建水平分隔的主内容区域
        self.content_frame = tk.Frame(
            self.main_frame,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True)
        
        # 左侧区域 - API商品主图预览
        if self.api_image_url:
            self.api_preview_frame = tk.Frame(
                self.content_frame,
                bg=self.style_config.COLOR_SCHEME['secondary_bg'],
                bd=1,
                relief=tk.GROOVE
            )
            self.api_preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10), pady=0)

            # API图片标题
            self.api_title_label = tk.Label(
                self.api_preview_frame,
                text="API商品主图（参考）",
                font=self.style_config.FONT_SETTINGS['normal'],
                bg=self.style_config.COLOR_SCHEME['accent_blue'],
                fg='white',
                padx=10,
                pady=5
            )
            self.api_title_label.pack(fill=tk.X)

            # API图片预览区
            self.api_image_frame = tk.Frame(
                self.api_preview_frame,
                bg=self.style_config.COLOR_SCHEME['secondary_bg']
            )
            self.api_image_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            self.api_image_label = tk.Label(
                self.api_image_frame,
                bg=self.style_config.COLOR_SCHEME['secondary_bg'],
                text="加载中...",
                font=self.style_config.FONT_SETTINGS['subtitle']
            )
            self.api_image_label.pack(fill=tk.BOTH, expand=True)
            
            # 启动线程加载API图片
            threading.Thread(target=self.load_api_image, daemon=True).start()
        
        # 缩略图区域 - 使用Canvas和Scrollbar实现垂直滚动
        self.thumbnails_canvas_frame = tk.Frame(
            self.content_frame,
            bg=self.style_config.COLOR_SCHEME['secondary_bg'],
            bd=1,
            relief=tk.GROOVE,
            width=200  # 增加宽度
        )
        self.thumbnails_canvas_frame.pack(side=tk.RIGHT, fill=tk.BOTH, pady=0)
        self.thumbnails_canvas_frame.pack_propagate(False)  # 防止frame被内容撑开

        # 缩略图标题
        self.thumb_title_label = tk.Label(
            self.thumbnails_canvas_frame,
            text="本地缩略图",
            font=self.style_config.FONT_SETTINGS['normal'],
            bg=self.style_config.COLOR_SCHEME['accent_orange'],
            fg='white',
            padx=10,
            pady=5
        )
        self.thumb_title_label.pack(fill=tk.X)

        self.thumbnails_canvas = tk.Canvas(
            self.thumbnails_canvas_frame,
            bg=self.style_config.COLOR_SCHEME['secondary_bg'],
            width=180  # 增加宽度
        )
        self.thumbnails_scrollbar = tk.Scrollbar(
            self.thumbnails_canvas_frame,
            orient=tk.VERTICAL,
            command=self.thumbnails_canvas.yview
        )

        self.thumbnails_frame = tk.Frame(
            self.thumbnails_canvas,
            bg=self.style_config.COLOR_SCHEME['secondary_bg']
        )
        
        self.thumbnails_canvas.configure(yscrollcommand=self.thumbnails_scrollbar.set)
        
        # 布局滚动区域
        self.thumbnails_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.thumbnails_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0), pady=5)
        
        # 创建窗口
        self.thumbnails_canvas.create_window(
            (0, 0),
            window=self.thumbnails_frame,
            anchor=tk.NW,
            width=self.thumbnails_canvas.winfo_reqwidth()
        )
        
        # 创建浮动预览窗口（初始隐藏）
        self.popup_preview = None
        self.popup_window = None
        
        # 按钮区域
        self.button_frame = tk.Frame(
            self.main_frame,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.button_frame.pack(fill=tk.X, pady=(15, 0))

        # API图片下载按钮
        if self.api_image_url:
            self.api_download_button = tk.Button(
                self.button_frame,
                text="使用API图片",
                font=self.style_config.FONT_SETTINGS['button'],
                bg=self.style_config.COLOR_SCHEME['accent_blue'],
                fg='white',
                relief=tk.FLAT,
                padx=20,
                pady=5,
                command=self.on_api_download
            )
            self.api_download_button.pack(side=tk.LEFT, padx=5)

        # 取消按钮
        self.cancel_button = tk.Button(
            self.button_frame,
            text="取消",
            font=self.style_config.FONT_SETTINGS['button'],
            bg=self.style_config.COLOR_SCHEME['accent_gray'],
            fg='white',
            relief=tk.FLAT,
            padx=20,
            pady=5,
            command=self.on_cancel
        )
        self.cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # 设置居中
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f'{width}x{height}+{x}+{y}')
        
        # 绑定滚动事件
        self.thumbnails_frame.bind("<Configure>", self.on_frame_configure)
    
    def load_api_image(self):
        """在后台线程中加载API图片"""
        if not self.api_image_url:
            return
            
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.example.com/',
                'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
            }
            
            response = requests.get(self.api_image_url, headers=headers, timeout=15)
            response.raise_for_status()
            
            # 从响应内容创建图像
            img = Image.open(io.BytesIO(response.content))
            
            # 创建缩略图
            api_image = self.create_thumbnail(img, (600, 550))  # 调整大小
            
            # 保存API图片数据，用于后续使用
            self.api_image_data = img
            
            # 更新UI（必须在主线程中）
            self.dialog.after(0, lambda: self.update_api_image(api_image))
            
        except Exception as e:
            print(f"加载API图片错误: {e}")
            # 更新UI（必须在主线程中）
            self.dialog.after(0, lambda: self.api_image_label.config(text=f"无法加载API图片: {str(e)}"))
    
    def update_api_image(self, image):
        """更新API图片显示（在主线程中调用）"""
        if hasattr(self, 'api_image_label'):
            self.api_image_label.config(image=image)
            self.api_image_label.image = image  # 保持引用
        
    def on_api_download(self):
        """选择使用API图片"""
        self.selected_index = -1  # 使用-1表示选择API图片
        self.dialog.destroy()
        if self.callback:
            self.callback(-1)

    def on_frame_configure(self, event):
        """更新滚动区域"""
        self.thumbnails_canvas.configure(scrollregion=self.thumbnails_canvas.bbox("all"))

    def load_images(self):
        """加载图片"""
        self.image_queue = queue.Queue()
        self.thumbnail_refs = []  # 保持引用，防止垃圾回收
        
        # 启动线程加载图片
        threading.Thread(target=self._load_images_thread, daemon=True).start()
        
        # 设置定时器检查队列
        self.check_queue()
        
    def _load_images_thread(self):
        """在后台线程中加载图片"""
        for idx, (item, file_path) in enumerate(self.image_files):
            try:
                # 使用Everything API获取图片
                image_url = f"http://127.0.0.1:8080/{quote(file_path)}"
                response = requests.get(image_url, timeout=10)
                response.raise_for_status()
                
                # 从响应内容创建图像
                img = Image.open(io.BytesIO(response.content))
                
                # 创建缩略图和悬停预览图
                thumbnail = self.create_thumbnail(img, (120, 120))  # 缩略图
                hover_image = self.create_thumbnail(img, (500, 400))  # 增大悬停预览大小
                
                # 将结果放入队列
                self.image_queue.put((idx, thumbnail, hover_image, file_path))
                
            except Exception as e:
                print(f"加载图片错误: {e}")
                # 加载失败时将错误信息放入队列
                self.image_queue.put((idx, None, None, None))
        
    def check_queue(self):
        """检查图片加载队列"""
        try:
            while not self.image_queue.empty():
                idx, thumbnail, hover_image, file_path = self.image_queue.get_nowait()
                
                if thumbnail:
                    # 创建缩略图容器
                    thumb_container = tk.Frame(
                        self.thumbnails_frame,
                        bg=self.style_config.COLOR_SCHEME['secondary_bg']
                    )
                    thumb_container.pack(side=tk.TOP, pady=10, fill=tk.X)

                    # 创建缩略图标签
                    thumb_label = tk.Label(
                        thumb_container,
                        image=thumbnail,
                        bg=self.style_config.COLOR_SCHEME['secondary_bg'],
                        bd=2,
                        relief=tk.FLAT
                    )
                    thumb_label.image = thumbnail  # 保持引用
                    thumb_label.pack(side=tk.TOP)
                    # 绑定右键点击事件
                    thumb_label.bind("<Button-3>", lambda e, i=idx: self.on_right_click(i))

                    # 绑定鼠标悬停事件
                    thumb_label.bind("<Enter>", lambda e, img=hover_image, path=file_path: self.show_hover_preview(e, img, path))
                    thumb_label.bind("<Leave>", self.hide_hover_preview)

                    # 创建选择按钮
                    select_btn = tk.Button(
                        thumb_container,
                        text=f"选择 {idx+1}",
                        font=self.style_config.FONT_SETTINGS['normal'],
                        bg=self.style_config.COLOR_SCHEME['accent_blue'],
                        fg='white',
                        relief=tk.FLAT,
                        command=lambda i=idx: self.on_select(i)
                    )
                    select_btn.pack(side=tk.TOP, pady=5)
                    
                    self.thumbnail_refs.append((thumb_label, select_btn, thumbnail, hover_image))
                    
            # 如果队列为空且没有加载任何图片，显示提示
            if not self.thumbnail_refs and not self.api_image_url:
                self.thumb_title_label.config(text="没有找到可用的图片")
                
        except Exception as e:
            print(f"处理图片队列错误: {e}")
            
        finally:
            # 每100毫秒检查一次队列
            self.dialog.after(100, self.check_queue)
            
    def create_thumbnail(self, img, size):
        """创建缩略图"""
        # 计算等比例缩放
        img_copy = img.copy()  # 创建副本
        img_copy.thumbnail(size, Image.LANCZOS)
        
        # 创建Tkinter可用的图像对象
        return ImageTk.PhotoImage(img_copy)
        
    def format_size(self, bytes_size):
        """格式化文件大小"""
        if bytes_size < 1024:
            return f"{bytes_size} B"
        elif bytes_size < 1024 * 1024:
            return f"{bytes_size / 1024:.2f} KB"
        elif bytes_size < 1024 * 1024 * 1024:
            return f"{bytes_size / (1024 * 1024):.2f} MB"
        else:
            return f"{bytes_size / (1024 * 1024 * 1024):.2f} GB"
            
    def on_thumbnail_click(self, idx):
        """点击缩略图时显示浮动预览"""
        try:
            # 获取所选图片的信息
            _, file_path = self.image_files[idx]
            
            # 显示悬停预览
            _, _, _, hover_image = self.thumbnail_refs[idx]
            if hover_image:
                self.show_hover_preview(None, hover_image, file_path)
                
        except Exception as e:
            print(f"显示预览错误: {e}")

    def on_right_click(self, idx):
        """右键点击选择图片"""
        self.on_select(idx)
        
    def on_select(self, idx):
        """选择图片"""
        self.selected_index = idx
        self.dialog.destroy()
        if self.callback:
            self.callback(idx)
            
    def on_cancel(self):
        """取消选择"""
        self.dialog.destroy()
        if self.callback:
            self.callback(None)
            
    def wait_for_selection(self):
        """等待用户选择"""
        self.dialog.wait_window()
        return self.selected_index

    def show_hover_preview(self, event, hover_image, file_path):
        """显示悬停预览窗口"""
        if self.popup_window:
            self.hide_hover_preview(None)
            
        # 创建一个顶层窗口
        self.popup_window = tk.Toplevel(self.dialog)
        self.popup_window.overrideredirect(True)  # 移除窗口边框
        self.popup_window.configure(bg=self.style_config.COLOR_SCHEME['secondary_bg'])
        self.popup_window.wm_attributes("-topmost", 1)  # 置顶
        
        # 获取鼠标在屏幕上的位置
        x = self.dialog.winfo_pointerx() + 15
        y = self.dialog.winfo_pointery() - 200  # 向上偏移，避免遮挡缩略图
        
        # 确保预览窗口在屏幕内
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()
        
        if hover_image:
            img_width = hover_image.width()
            img_height = hover_image.height()
            
            # 调整位置，确保在屏幕内
            if x + img_width > screen_width:
                x = screen_width - img_width - 20
            if y + img_height > screen_height:
                y = screen_height - img_height - 20
            if y < 0:
                y = 0
        
        # 设置窗口位置
        self.popup_window.geometry(f"+{x}+{y}")
        
        # 创建图像标签
        if hover_image:
            self.popup_preview = tk.Label(
                self.popup_window,
                image=hover_image,
                bg=self.style_config.COLOR_SCHEME['secondary_bg'],
                bd=1,
                relief=tk.SOLID
            )
            self.popup_preview.image = hover_image  # 保持引用
            self.popup_preview.pack()

    def hide_hover_preview(self, event):
        """隐藏悬停预览窗口"""
        if self.popup_window:
            self.popup_window.destroy()
            self.popup_window = None
            self.popup_preview = None

class StyleConfig:
    """现代化界面样式配置"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.update_from_config()

    def update_from_config(self):
        """从配置文件更新样式"""
        self.FONT_NAME = self.config_manager.get('样式配置', '字体名称', '微软雅黑')

        self.COLOR_SCHEME = {
            'primary_bg': self.config_manager.get('样式配置', '主背景色', '#F5F7FA'),
            'secondary_bg': self.config_manager.get('样式配置', '次背景色', '#FFFFFF'),
            'primary_text': self.config_manager.get('样式配置', '主文字色', '#2D3748'),
            'secondary_text': self.config_manager.get('样式配置', '次文字色', '#4A5568'),
            'accent_blue': self.config_manager.get('样式配置', '主色调蓝', '#4299E1'),
            'accent_green': self.config_manager.get('样式配置', '成功色绿', '#48BB78'),
            'accent_orange': self.config_manager.get('样式配置', '警告色橙', '#ED8936'),
            'accent_gray': self.config_manager.get('样式配置', '禁用色灰', '#A0AEC0'),
            'console_bg': self.config_manager.get('样式配置', '控制台背景色', '#FFFFFF'),
            'border': self.config_manager.get('样式配置', '边框色', '#E2E8F0')
        }

        self.FONT_SETTINGS = {
            'title': (self.FONT_NAME, 14, 'bold'),
            'subtitle': (self.FONT_NAME, 11),
            'normal': (self.FONT_NAME, 10),
            'button': (self.FONT_NAME, 10, 'bold'),
            'console': (self.FONT_NAME, 9),
            'status': (self.FONT_NAME, 9)
        }


class ImageDownloader:
    """图片下载功能模块"""

    def __init__(self, config_manager):
        self.config_manager = config_manager
        self.update_from_config()

    def update_from_config(self):
        """从配置文件更新设置"""
        self.target_path_suffix = self.config_manager.get('路径配置', '目标路径后缀', r'\导出图\已完成')
        self.enable_path_suffix = self.config_manager.get_bool('路径配置', '启用目标路径后缀', True)
        formats_str = self.config_manager.get('文件配置', '支持的图片格式', '.png,.jpg,.jpeg')
        self.supported_formats = tuple(fmt.strip().lower() for fmt in formats_str.split(','))

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    def should_download_file(self, file_path, file_name):
        """检查文件是否符合下载条件"""
        # 检查文件格式
        if not os.path.splitext(file_name)[1].lower() in self.supported_formats:
            return False

        # 如果启用了目标路径后缀限制，则检查路径
        if self.enable_path_suffix and self.target_path_suffix:
            # 将文件路径转换为小写以进行不区分大小写的比较
            lower_file_path = file_path.lower()
            lower_suffix = self.target_path_suffix.lower()

            # 检查文件路径是否包含目标路径后缀
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False

        return True

    @staticmethod
    def prepare_directory(base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir


class ModernJsonParserApp:
    def __init__(self, root):
        self.root = root
        self.current_output_dir = ""

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 初始化日志管理器
        self.log_manager = LogManager()

        # 初始化样式配置
        self.style_config = StyleConfig(self.config_manager)

        # 初始化图片下载器
        self.image_downloader = ImageDownloader(self.config_manager)

        self.setup_window()
        self.create_widgets()
        self.setup_layout()
        # 设置默认值
        self.set_default_values()

        # 绑定配置变更事件
        self.bind_config_events()

    def setup_window(self):
        """窗口基础配置"""
        title = self.config_manager.get('界面配置', '窗口标题', '商品数据提取工具')
        geometry = self.config_manager.get('界面配置', '窗口大小', '1000x750')
        min_size = self.config_manager.get('界面配置', '最小窗口大小', '800x600')

        self.root.title(title)
        self.root.geometry(geometry)

        # 解析最小窗口大小
        try:
            min_width, min_height = map(int, min_size.split('x'))
            self.root.minsize(min_width, min_height)
        except:
            self.root.minsize(800, 600)

        self.root.configure(bg=self.style_config.COLOR_SCHEME['primary_bg'])

        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

    def create_widgets(self):
        """创建所有界面组件"""
        # 主容器
        self.main_container = tk.Frame(
            self.root,
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            padx=20,
            pady=15
        )

        # 标题区域
        self.title_frame = tk.Frame(
            self.main_container,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.title_label = tk.Label(
            self.title_frame,
            text=self.config_manager.get('界面配置', '窗口标题', '商品数据提取工具'),
            font=self.style_config.FONT_SETTINGS['title'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.subtitle_label = tk.Label(
            self.title_frame,
            text="提取商品信息并下载图片",
            font=self.style_config.FONT_SETTINGS['subtitle'],
            fg=self.style_config.COLOR_SCHEME['secondary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )

        # 配置区域
        self.config_frame = self.create_label_frame(" 配置选项 ")
        
        # 搜索路径配置
        self.path_frame = tk.Frame(
            self.config_frame,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.search_path_label = tk.Label(
            self.path_frame,
            text="搜索路径：",
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.search_path_entry = tk.Entry(
            self.path_frame,
            font=self.style_config.FONT_SETTINGS['normal'],
            bg=self.style_config.COLOR_SCHEME['secondary_bg'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            width=50
        )
        self.browse_path_btn = tk.Button(
            self.path_frame,
            text="浏览",
            font=self.style_config.FONT_SETTINGS['normal'],
            bg=self.style_config.COLOR_SCHEME['accent_blue'],
            fg='white',
            relief=tk.FLAT,
            padx=10,
            command=self.browse_search_path
        )
        
        # 路径后缀限制配置
        self.filter_frame = tk.Frame(
            self.config_frame,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.path_filter_label = tk.Label(
            self.filter_frame,
            text="目标路径后缀：",
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            width=15,
            anchor='e'
        )
        self.path_filter_entry = tk.Entry(
            self.filter_frame,
            font=self.style_config.FONT_SETTINGS['normal'],
            bg=self.style_config.COLOR_SCHEME['secondary_bg'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            width=40
        )

        # 启用目标路径后缀的复选框
        self.enable_path_suffix_var = tk.BooleanVar(value=True)
        self.enable_path_suffix_cb = tk.Checkbutton(
            self.filter_frame,
            text="启用",
            variable=self.enable_path_suffix_var,
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            selectcolor=self.style_config.COLOR_SCHEME['secondary_bg'],
            command=self.on_enable_path_suffix_change
        )
        
        # 其他搜索选项
        self.options_frame = tk.Frame(
            self.config_frame,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )

        self.strict_search_var = tk.BooleanVar(value=True)
        self.strict_search_cb = tk.Checkbutton(
            self.options_frame,
            text="严格搜索路径限制（只搜索指定目录）",
            variable=self.strict_search_var,
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            selectcolor=self.style_config.COLOR_SCHEME['secondary_bg']
        )

        self.help_text = tk.Label(
            self.options_frame,
            text="注意：勾选\"严格搜索路径限制\"可确保只在指定目录中搜索",
            font=(self.style_config.FONT_NAME, 9, "italic"),
            fg=self.style_config.COLOR_SCHEME['accent_orange'],
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )

        # 输入区域
        self.input_frame = self.create_label_frame(" JSON输入 ")
        self.text_area = scrolledtext.ScrolledText(
            self.input_frame,
            wrap=tk.WORD,
            font=self.style_config.FONT_SETTINGS['normal'],
            bg=self.style_config.COLOR_SCHEME['secondary_bg'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            padx=15,
            pady=15,
            height=8,
            insertbackground=self.style_config.COLOR_SCHEME['primary_text'],
            selectbackground=self.style_config.COLOR_SCHEME['accent_blue']
        )

        # 按钮区域
        self.btn_frame = tk.Frame(
            self.main_container,
            bg=self.style_config.COLOR_SCHEME['primary_bg']
        )
        self.paste_btn = self.create_button("一键粘贴", self.style_config.COLOR_SCHEME['accent_blue'],
                                            self.paste_from_clipboard)
        self.download_btn = self.create_button("解析并下载", self.style_config.COLOR_SCHEME['accent_green'],
                                               self.download_product_images)
        self.rename_btn = self.create_button("重命名文件", self.style_config.COLOR_SCHEME['accent_blue'],
                                            self.rename_files)

        # 控制台输出
        self.console_frame = self.create_label_frame(" 操作日志 ")
        self.console = scrolledtext.ScrolledText(
            self.console_frame,
            wrap=tk.WORD,
            font=self.style_config.FONT_SETTINGS['console'],
            bg=self.style_config.COLOR_SCHEME['console_bg'],
            fg=self.style_config.COLOR_SCHEME['secondary_text'],
            state='disabled',
            padx=15,
            pady=15
        )

        # 状态栏
        self.status_bar = tk.Label(
            self.root,
            text="就绪",
            font=self.style_config.FONT_SETTINGS['status'],
            fg=self.style_config.COLOR_SCHEME['secondary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            bd=1,
            relief=tk.SUNKEN,
            anchor=tk.W
        )

    def setup_layout(self):
        """组件布局"""
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # 标题区域
        self.title_frame.pack(fill=tk.X, pady=(0, 10))
        self.title_label.pack(side=tk.TOP, anchor='w')
        self.subtitle_label.pack(side=tk.TOP, anchor='w')
        
        # 配置区域
        self.config_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 搜索路径配置
        self.path_frame.pack(fill=tk.X, pady=(5, 5))
        self.search_path_label.pack(side=tk.LEFT)
        self.search_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.browse_path_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 路径后缀限制配置
        self.filter_frame.pack(fill=tk.X, pady=(0, 5))
        self.path_filter_label.pack(side=tk.LEFT)
        self.path_filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        self.enable_path_suffix_cb.pack(side=tk.LEFT, padx=(5, 0))
        
        # 其他搜索选项
        self.options_frame.pack(fill=tk.X, pady=(0, 5))
        self.strict_search_cb.pack(side=tk.LEFT, padx=(15, 0))
        self.help_text.pack(side=tk.LEFT, padx=(10, 0))

        # 输入区域
        self.input_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
        self.text_area.pack(fill=tk.BOTH, expand=True)

        # 按钮区域
        self.btn_frame.pack(fill=tk.X, pady=(0, 15))
        self.paste_btn.pack(side=tk.LEFT, padx=5)
        self.download_btn.pack(side=tk.LEFT, padx=5)
        self.rename_btn.pack(side=tk.LEFT, padx=5)

        # 控制台输出
        self.console_frame.pack(fill=tk.BOTH, expand=True)
        self.console.pack(fill=tk.BOTH, expand=True)

        # 状态栏
        self.status_bar.pack(fill=tk.X, padx=20, pady=(0, 10))
        
    def set_default_values(self):
        """设置默认值"""
        search_path = self.config_manager.get('路径配置', '搜索基础路径', r'E:\图片\原图')
        path_filter = self.config_manager.get('路径配置', '目标路径后缀', r'\导出图\已完成')
        strict_search = self.config_manager.get_bool('搜索配置', '严格搜索路径限制', True)
        enable_path_suffix = self.config_manager.get_bool('路径配置', '启用目标路径后缀', True)

        self.search_path_entry.insert(0, search_path)
        self.path_filter_entry.insert(0, path_filter)
        self.strict_search_var.set(strict_search)
        self.enable_path_suffix_var.set(enable_path_suffix)

        # 根据启用状态设置输入框状态
        self.update_path_filter_state()

    def bind_config_events(self):
        """绑定配置变更事件"""
        # 绑定输入框变更事件
        self.search_path_entry.bind('<KeyRelease>', self.on_search_path_change)
        self.search_path_entry.bind('<FocusOut>', self.on_search_path_change)

        self.path_filter_entry.bind('<KeyRelease>', self.on_path_filter_change)
        self.path_filter_entry.bind('<FocusOut>', self.on_path_filter_change)

        # 绑定复选框变更事件
        self.strict_search_var.trace('w', self.on_strict_search_change)

    def on_search_path_change(self, event=None):
        """搜索路径变更事件"""
        new_path = self.search_path_entry.get().strip()
        self.config_manager.set('路径配置', '搜索基础路径', new_path)

    def on_path_filter_change(self, event=None):
        """路径过滤器变更事件"""
        new_filter = self.path_filter_entry.get().strip()
        self.config_manager.set('路径配置', '目标路径后缀', new_filter)
        # 更新图片下载器配置
        self.image_downloader.update_from_config()

    def on_strict_search_change(self, *args):
        """严格搜索变更事件"""
        new_value = self.strict_search_var.get()
        self.config_manager.set('搜索配置', '严格搜索路径限制', str(new_value))
        self.log_manager.log_info(f"严格搜索路径限制设置为: {new_value}")

    def on_enable_path_suffix_change(self):
        """启用目标路径后缀变更事件"""
        new_value = self.enable_path_suffix_var.get()
        self.config_manager.set('路径配置', '启用目标路径后缀', str(new_value))
        self.image_downloader.update_from_config()
        self.update_path_filter_state()
        self.log_manager.log_info(f"启用目标路径后缀设置为: {new_value}")

    def update_path_filter_state(self):
        """更新路径过滤器输入框状态"""
        if self.enable_path_suffix_var.get():
            self.path_filter_entry.config(state='normal')
            self.path_filter_label.config(fg=self.style_config.COLOR_SCHEME['primary_text'])
        else:
            self.path_filter_entry.config(state='disabled')
            self.path_filter_label.config(fg=self.style_config.COLOR_SCHEME['accent_gray'])

    def create_label_frame(self, text):
        """创建统一风格的标签框架"""
        return tk.LabelFrame(
            self.main_container,
            text=text,
            font=self.style_config.FONT_SETTINGS['normal'],
            fg=self.style_config.COLOR_SCHEME['primary_text'],
            bg=self.style_config.COLOR_SCHEME['primary_bg'],
            padx=10,
            pady=10,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=self.style_config.COLOR_SCHEME['border']
        )

    def create_button(self, text, bg_color, command):
        """创建统一风格的按钮"""
        return tk.Button(
            self.btn_frame,
            text=text,
            font=self.style_config.FONT_SETTINGS['button'],
            bg=bg_color,
            fg='#FFFFFF',
            activebackground=bg_color,
            activeforeground='#FFFFFF',
            relief=tk.FLAT,
            padx=20,
            pady=8,
            command=command
        )

    def log(self, message, message_type='info'):
        """控制台日志输出，同时记录到文件"""
        color_map = {
            'info': self.style_config.COLOR_SCHEME['secondary_text'],
            'success': self.style_config.COLOR_SCHEME['accent_green'],
            'warning': self.style_config.COLOR_SCHEME['accent_orange'],
            'error': '#E53E3E'
        }

        # 界面显示
        self.console.config(state='normal')
        self.console.insert(tk.END, message + "\n", message_type)
        self.console.tag_config(message_type, foreground=color_map.get(message_type, 'black'))
        self.console.see(tk.END)
        self.console.config(state='disabled')
        self.root.update()

        # 记录到文件
        if message_type == 'success':
            self.log_manager.log_success(message)
        elif message_type == 'warning':
            self.log_manager.log_warning(message)
        elif message_type == 'error':
            self.log_manager.log_error(message)
        else:
            self.log_manager.log_info(message)

    def update_status(self, message):
        """更新状态栏"""
        self.status_bar.config(text=message)
        self.root.update()

    def paste_from_clipboard(self):
        """从剪贴板粘贴内容"""
        try:
            self.text_area.delete("1.0", tk.END)
            text = self.root.clipboard_get()
            self.text_area.insert(tk.END, text)
            self.log("√ 已从剪贴板粘贴内容", 'success')
            self.log_manager.log_info(f"从剪贴板粘贴内容，长度: {len(text)} 字符")
            self.update_status("已粘贴剪贴板内容")
        except tk.TclError:
            self.log_manager.log_warning("剪贴板中没有内容")
            messagebox.showwarning("警告", "剪贴板中没有内容")

    def download_product_images(self):
        """下载商品图片（整合解析JSON功能）"""
        try:
            # 首先解析JSON数据
            self.log("\n" + "=" * 50 + "\n开始解析JSON数据...", 'info')
            self.log_manager.log_info("开始解析JSON数据和下载商品图片")
            self.update_status("正在解析数据...")

            json_str = self.text_area.get("1.0", tk.END).strip()
            if not json_str:
                self.log_manager.log_warning("用户未输入JSON内容")
                messagebox.showwarning("警告", "请先粘贴JSON内容")
                return

            self.log_manager.log_info(f"获取到JSON内容，长度: {len(json_str)} 字符")
            try:
                data = json.loads(json_str)
                self.log_manager.log_info("JSON解析成功")
            except json.JSONDecodeError:
                # 如果不是JSON格式，尝试从已有的输出目录读取商品列表
                if not self.current_output_dir:
                    messagebox.showwarning("警告", "无效的JSON格式，请提供正确的JSON数据")
                    self.log("× JSON解析失败: 无效的JSON格式", 'error')
                    return
                
                # 从已有目录读取商品列表
                txt_path = os.path.join(self.current_output_dir, "商品列表.txt")
                if not os.path.exists(txt_path):
                    messagebox.showerror("错误", "找不到商品列表文件，请重新解析数据")
                    return
                
                products = self.read_product_list(txt_path)
                if not products:
                    messagebox.showwarning("警告", "商品列表中没有有效数据")
                    return
                
                # 直接进入下载流程
                self.start_download_process(products)
                return

            # 获取原始订单数据
            raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
            original_count = len(raw_orders)
            self.log_manager.log_info(f"获取到原始订单数据: {original_count} 条")

            # 过滤首单订单
            sub_orders = [order for order in raw_orders if order.get('isFirst')]
            first_order_count = len(sub_orders)
            self.log_manager.log_info(f"过滤后的首单订单数: {first_order_count} 条")

            # 显示订单数量对比
            self.log(f"订单统计: 原始订单数={original_count} | 首单数={first_order_count}", 'info')

            if not sub_orders:
                self.log_manager.log_error("未找到符合条件的首单订单")
                messagebox.showwarning("警告", "未找到首单订单数据")
                self.log("× 未找到符合条件的首单订单", 'error')
                return

            # 创建时间戳和输出目录
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"商品数据_{timestamp}_共{first_order_count}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name
            self.log(f"创建输出文件夹: {folder_name}", 'info')

            txt_path = os.path.join(folder_name, "商品列表.txt")
            error_log = []
            success_count = 0
            products = []

            with open(txt_path, 'w', encoding='utf-8') as f:
                for index, order in enumerate(sub_orders, 1):
                    try:
                        product_name = order.get('productName', '未知商品').strip()
                        sku_id = self.get_fulfilment_id(order)
                        image_url = order.get('productSkcPicture', '').strip()

                        self.log_manager.log_info(f"处理第 {index}/{first_order_count} 条订单数据")
                        self.log_manager.log_info(f"商品名称: {product_name}")
                        self.log_manager.log_info(f"SKU ID: {sku_id}")
                        self.log_manager.log_info(f"图片URL: {image_url}")

                        # 写入文本文件
                        f.write(f"{product_name}----{sku_id}\n")
                        products.append((product_name, sku_id))

                        # 下载图片
                        if image_url and sku_id not in ["无FulfilmentID", "错误ID"]:
                            self.log(f"\n处理第 {index}/{first_order_count} 条数据:", 'info')
                            self.log(f"商品名称: {product_name}")
                            self.log(f"SKU ID: {sku_id}")

                            # 获取图片扩展名
                            ext = os.path.splitext(image_url)[1].split('?')[0]
                            if not ext:
                                ext = self.detect_image_extension(image_url)

                            img_path = os.path.join(folder_name, f"{sku_id}{ext}")

                            if self.download_image(image_url, img_path):
                                self.log("√ 图片下载成功", 'success')
                                self.log_manager.log_success(f"商品图片下载成功: {sku_id}{ext}")
                                success_count += 1
                            else:
                                error_msg = f"× 图片下载失败: {image_url}"
                                self.log(error_msg, 'error')
                                self.log_manager.log_error(f"商品图片下载失败: {image_url}")
                                error_log.append(error_msg)
                        else:
                            self.log(f"! 跳过无效图片URL或ID: {image_url}", 'warning')
                            self.log_manager.log_warning(f"跳过无效图片URL或ID: {image_url}")
                            success_count += 1

                    except Exception as e:
                        error_msg = f"第{index}条处理失败: {str(e)}"
                        self.log(f"× {error_msg}", 'error')
                        self.log_manager.log_error(error_msg)
                        error_log.append(error_msg)
                        continue

            # 生成报告
            report = [
                "\n" + "=" * 50,
                "解析完成！",
                f"保存路径: {os.path.abspath(folder_name)}",
                f"原始订单总数: {original_count}",
                f"首单数量: {first_order_count}",
                f"成功处理: {success_count}",
                f"失败: {len(error_log)}"
            ]

            if error_log:
                error_path = os.path.join(folder_name, "error.log")
                with open(error_path, 'w', encoding='utf-8') as err_file:
                    err_file.write("\n".join(error_log))
                report.append(f"错误日志: {error_path}")

            self.log("\n".join(report), 'info')
            
            # 继续下载图片流程
            self.start_download_process(products)

        except Exception as e:
            messagebox.showerror("错误", f"系统异常: {str(e)}")
            self.log(f"× 系统异常: {str(e)}", 'error')

    def start_download_process(self, products):
        """开始下载图片流程"""
        try:
            # 更新配置（从用户输入获取）
            search_path = self.search_path_entry.get().strip()
            path_filter = self.path_filter_entry.get().strip()
            enable_path_suffix = self.enable_path_suffix_var.get()

            self.log_manager.log_info(f"开始下载图片流程，商品数量: {len(products)}")
            self.log_manager.log_info(f"当前配置 - 搜索路径: {search_path}")
            self.log_manager.log_info(f"当前配置 - 目标路径后缀: {path_filter}")
            self.log_manager.log_info(f"当前配置 - 启用目标路径后缀: {enable_path_suffix}")

            # 更新下载器配置
            if path_filter:
                # 确保路径过滤器格式正确（不管用户是否输入了反斜杠）
                if not path_filter.startswith('\\'):
                    path_filter = '\\' + path_filter
                self.config_manager.set('路径配置', '目标路径后缀', path_filter)
                self.image_downloader.update_from_config()

            # 简化配置信息
            self.log(f"📝 当前配置: 搜索路径={search_path}, 目标路径={self.image_downloader.target_path_suffix}", 'info')

            if not products:
                messagebox.showwarning("警告", "商品列表中没有有效数据")
                return

            self.log("\n" + "═" * 60 + "\n开始处理商品图片...", 'info')
            self.update_status("正在处理商品图片...")

            base_dir = os.path.join(self.current_output_dir, "出单图下载")
            os.makedirs(base_dir, exist_ok=True)

            for name, pid in products:
                result = self.process_product(name, pid, base_dir, search_path)
                self.log(result, 'info')

            self.log("═" * 60 + "\n图片处理完成！", 'success')
            self.update_status("图片处理完成")
            messagebox.showinfo("完成", "商品图片处理完成！")

        except Exception as e:
            messagebox.showerror("错误", f"处理失败: {str(e)}")
            self.log(f"× 处理失败: {str(e)}", 'error')

    # 添加API图片下载重试机制
    def download_api_image(self, url, save_path, max_retries=3, retry_interval=2):
        """下载API图片，带重试机制
        
        Args:
            url: 图片URL
            save_path: 保存路径
            max_retries: 最大重试次数
            retry_interval: 重试间隔(秒)
            
        Returns:
            bool: 是否下载成功
        """
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API图片...", 'info')
                    time.sleep(retry_interval)
                
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.log(f"✅ API图片下载成功", 'success')
                    return True
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API图片失败: {str(e)}", 'error')
        
        return False

    def read_product_list(self, file_path):
        """读取商品列表文件"""
        products = []
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if '----' in line:
                        name, product_id = line.split('----', 1)
                        products.append((name, product_id))
            return products
        except Exception as e:
            raise RuntimeError(f"读取商品列表失败: {str(e)}")

    def process_product(self, product_name, product_id, base_dir, search_path=""):
        """处理单个商品"""
        try:
            # 记录开始处理商品
            self.log_manager.log_info(f"开始处理商品: {product_name} (ID: {product_id})")

            # 移除硬编码的路径前缀
            # 只保留商品名称作为搜索条件
            cleaned_name = product_name.replace("2D Flat ", "").strip()
            self.log_manager.log_info(f"清理后的商品名称: {cleaned_name}")

            # 构建限定路径的搜索查询
            search_query = cleaned_name

            # 如果指定了搜索路径并启用了严格路径搜索
            if search_path and self.strict_search_var.get():
                if not search_path.endswith('\\'):
                    search_path += '\\'
                # 使用Everything的path:语法限制搜索范围
                search_query = f"path:\"{search_path}\" {search_query}"
                self.log_manager.log_info(f"使用严格路径搜索: {search_query}")
                
            # EverythingSearch参数格式化
            search_params = {
                "search": search_query,
                "json": 1,
                "path_column": 1,
                "size_column": 1,
                "sort": "name",
                "ascending": 1
            }
            
            search_url = "http://localhost:8080/"
            
            # 简化日志输出
            self.log(f"🔍 搜索: {product_name}", 'info')
            self.log_manager.log_info(f"发送搜索请求: {search_query}")

            try:
                response = requests.get(
                    search_url,
                    params=search_params,
                    timeout=30
                )
                response.raise_for_status()  # 检查HTTP错误
                data = response.json()

                self.log_manager.log_info(f"搜索API请求成功，状态码: {response.status_code}")

            except requests.RequestException as e:
                self.log(f"❌ API请求失败: {str(e)}", 'error')
                self.log_manager.log_error(f"API请求失败: {str(e)}")
                return f"处理商品『{product_name}』失败: API请求错误 - {str(e)}"
            except json.JSONDecodeError as e:
                self.log(f"❌ JSON解析失败: {str(e)}", 'error')
                self.log(f"原始响应内容: {response.text[:200]}...", 'error')
                self.log_manager.log_error(f"JSON解析失败: {str(e)}")
                return f"处理商品『{product_name}』失败: JSON解析错误 - {str(e)}"
            
            # 添加调试信息
            total_results = len(data.get('results', []))
            self.log(f"📊 搜索结果: 找到 {total_results} 个匹配项", 'info')
            self.log_manager.log_info(f"搜索结果总数: {total_results}")

            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")

                self.log_manager.log_info(f"检查文件: {file_name} (路径: {file_path})")

                should_download = self.image_downloader.should_download_file(file_path, file_name)
                if should_download:
                    valid_files.append((item, file_path))
                    self.log_manager.log_info(f"文件符合条件: {file_name}")
                else:
                    self.log_manager.log_info(f"文件不符合条件: {file_name}")

            # 获取API图片URL，仅用于对比显示
            api_url = self.get_product_api_image(product_id)
            if api_url:
                self.log_manager.log_info(f"获取到API商品图URL: {api_url}")
            else:
                self.log_manager.log_info("未获取到API商品图URL")

            if not valid_files:
                # 如果本地没有找到匹配图片，尝试使用API图片
                self.log_manager.log_warning(f"本地未找到符合条件的图片文件")
                if api_url:
                    self.log(f"⚠️ 本地未找到图片，将使用API商品图", 'warning')
                    self.log_manager.log_info("尝试使用API商品图")
                    # 准备下载目录
                    target_dir = ImageDownloader.prepare_directory(base_dir)
                    new_name = f"{product_id}.jpg"
                    local_path = os.path.join(target_dir, new_name)

                    # 直接下载API图片
                    if self.download_api_image(api_url, local_path):
                        self.log(f"✅ 已使用API图片: {new_name}", 'success')
                        self.log_manager.log_success(f"API图片下载成功: {new_name}")

                        result = [
                            f"\n🔍 商品名称: {product_name}",
                            f"🆔 商品ID: {product_id}",
                            f"📁 本地未找到匹配图片，已使用API图片",
                            f"状态: ✓ 下载成功 → {new_name}",
                            f"📂 保存路径: {os.path.abspath(target_dir)}",
                            "═" * 60
                        ]
                        return "\n".join(result)
                    else:
                        self.log_manager.log_error("API图片下载失败")

                self.log_manager.log_warning(f"商品 {product_name} 未找到符合条件文件，且无法获取API图片")
                return f"『{product_name}』未找到符合条件文件，且无法获取API图片"

            # 使用图片选择对话框
            total_files = len(valid_files)

            # 如果有API图片URL，传递给选择对话框
            self.log(f"📊 找到 {total_files} 张匹配图片，打开预览窗口", 'info')
            self.log_manager.log_info(f"找到 {total_files} 张符合条件的图片，显示选择对话框")

            # 创建并显示图片选择对话框，传入API图片URL
            selection_dialog = ImageSelectionDialog(self.root, product_id, valid_files, None, api_url, self.style_config)
            selected_idx = selection_dialog.wait_for_selection()

            if selected_idx is not None:
                # 用户选择了图片
                if selected_idx == -1 and selection_dialog.api_image_data:
                    # 用户选择了API图片
                    self.log(f"用户选择了API商品图", 'info')
                    self.log_manager.log_info("用户选择了API商品图")
                    # 准备下载目录
                    target_dir = ImageDownloader.prepare_directory(base_dir)
                    new_name = f"{product_id}.jpg"
                    local_path = os.path.join(target_dir, new_name)
                    
                    # 直接保存API图片到目标目录
                    try:
                        selection_dialog.api_image_data.save(local_path)
                        status = f"√ 下载成功 → {new_name}"
                        self.log_manager.log_success(f"用户选择的API图片保存成功: {new_name}")

                        result = [
                            f"\n🔍 商品名称: {product_name}",
                            f"🆔 商品ID: {product_id}",
                            f"📁 用户选择了API商品图",
                            f"状态: {status}",
                            f"📊 最终结果: 成功下载 1 个文件",
                            f"📂 保存路径: {os.path.abspath(target_dir)}",
                            "═" * 60
                        ]
                        return "\n".join(result)
                    except Exception as e:
                        self.log(f"❌ 复制API图片失败: {str(e)}", 'error')
                        self.log_manager.log_error(f"复制API图片失败: {str(e)}")
                        return f"处理商品『{product_name}』出错: 复制API图片失败 - {str(e)}"
                else:
                    # 用户选择了本地图片
                    item, file_path = valid_files[selected_idx]
                    original_ext = os.path.splitext(item['name'])[1].lower()
                    new_name = f"{product_id}{original_ext}"

                    self.log_manager.log_info(f"用户选择了本地图片: {item['name']} (索引: {selected_idx})")

                    # 准备下载目录
                    target_dir = ImageDownloader.prepare_directory(base_dir)
                    local_path = os.path.join(target_dir, new_name)
                    image_url = f"http://127.0.0.1:8080/{quote(file_path)}"
                    
                    success = self.download_image(image_url, local_path)
                    status = f"√ 下载成功 → {new_name}" if success else "× 下载失败"
                    
                    result = [
                        f"\n🔍 商品名称: {product_name}",
                        f"🆔 商品ID: {product_id}",
                        f"📁 从 {total_files} 张图片中选择了第 {selected_idx+1} 张",
                        f"状态: {status}",
                        f"📊 最终结果: {'成功' if success else '失败'}下载 1 个文件",
                        f"📂 保存路径: {os.path.abspath(target_dir)}",
                        "═" * 60
                    ]
                    return "\n".join(result)
            else:
                # 用户取消了选择
                self.log(f"⚠️ 用户取消了图片选择", 'warning')
                return f"『{product_name}』用户取消了图片选择"

        except Exception as e:
            return f"处理商品『{product_name}』出错: {str(e)}"

    def get_product_api_image(self, product_id):
        """获取商品的API图片URL
        从已解析的JSON中查找对应的图片URL
        """
        try:
            # 从文本区域获取JSON数据
            json_str = self.text_area.get("1.0", tk.END).strip()
            if not json_str:
                return None
                
            # 尝试解析JSON
            try:
                data = json.loads(json_str)
            except json.JSONDecodeError:
                return None
                
            # 获取订单数据
            raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
            
            # 查找匹配的订单
            for order in raw_orders:
                if not order.get('isFirst'):
                    continue
                    
                # 检查是否匹配当前商品ID
                sku_list = order.get('skuQuantityDetailList', [])
                if not sku_list:
                    continue
                    
                sku_id = str(sku_list[0].get('fulfilmentProductSkuId', ''))
                if sku_id == product_id:
                    # 找到匹配的订单，返回图片URL
                    image_url = order.get('productSkcPicture', '')
                    if image_url:
                        self.log(f"✅ 找到API商品图URL", 'info')
                        return image_url
            
            return None
        except Exception as e:
            self.log(f"❌ 获取API图片URL失败: {str(e)}", 'error')
            return None

    def get_fulfilment_id(self, order):
        """获取fulfilmentProductSkuId"""
        try:
            sku_list = order.get('skuQuantityDetailList', [])
            if not sku_list:
                self.log("⚠️ skuQuantityDetailList为空", 'warning')
                return "无FulfilmentID"
            return str(sku_list[0].get('fulfilmentProductSkuId', '无FulfilmentID'))
        except Exception as e:
            self.log(f"获取fulfilmentID异常: {str(e)}", 'error')
            return "错误ID"

    def download_image(self, url, save_path):
        """下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            return False
        except Exception as e:
            self.log(f"下载异常: {str(e)}", 'error')
            return False

    def detect_image_extension(self, url):
        """根据URL猜测图片扩展名"""
        if 'jpeg' in url.lower() or 'jpg' in url.lower():
            return '.jpg'
        elif 'png' in url.lower():
            return '.png'
        elif 'gif' in url.lower():
            return '.gif'
        elif 'webp' in url.lower():
            return '.webp'
        return '.jpg'

    def rename_files(self):
        """重命名文件功能"""
        try:
            # 获取当前运行目录作为默认目录
            default_dir = os.path.dirname(os.path.abspath(__file__))
            
            folder_path = filedialog.askdirectory(
                title="请选择要处理的文件夹",
                initialdir=default_dir
            )
            if not folder_path:
                self.log("未选择文件夹，操作取消", 'warning')
                return

            self.log("\n" + "=" * 50 + "\n开始重命名文件...", 'info')
            self.update_status("正在重命名文件...")

            success_count = 0
            skip_count = 0
            error_count = 0

            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)

                # 跳过子目录
                if not os.path.isfile(file_path):
                    continue

                # 分离文件名和扩展名
                name_part, ext = os.path.splitext(filename)

                # 查找最后的下划线位置
                last_underscore = name_part.rfind('_')

                # 构建新文件名
                if last_underscore != -1:
                    new_name = name_part[:last_underscore] + ext
                    new_path = os.path.join(folder_path, new_name)

                    # 执行重命名（添加防冲突判断）
                    try:
                        if not os.path.exists(new_path):
                            os.rename(file_path, new_path)
                            self.log(f"成功: {filename} → {new_name}", 'success')
                            success_count += 1
                        else:
                            self.log(f"跳过: {new_name} 已存在（避免覆盖）", 'warning')
                            skip_count += 1
                    except Exception as e:
                        self.log(f"错误: 重命名 {filename} 失败 - {str(e)}", 'error')
                        error_count += 1
                else:
                    self.log(f"跳过: {filename} 无下划线", 'info')
                    skip_count += 1

            # 生成报告
            report = [
                "\n" + "=" * 50,
                "重命名完成！",
                f"处理文件夹: {folder_path}",
                f"成功重命名: {success_count}",
                f"跳过文件: {skip_count}",
                f"失败: {error_count}"
            ]

            self.log("\n".join(report), 'info')
            self.update_status("重命名完成")
            messagebox.showinfo("完成", "\n".join(report[1:]))

        except Exception as e:
            messagebox.showerror("错误", f"重命名过程出错: {str(e)}")
            self.log(f"× 重命名过程出错: {str(e)}", 'error')

    def browse_search_path(self):
        """浏览并选择搜索路径"""
        folder_path = filedialog.askdirectory(
            title="选择搜索路径",
            initialdir=self.search_path_entry.get() or os.path.dirname(os.path.abspath(__file__))
        )
        if folder_path:
            self.search_path_entry.delete(0, tk.END)
            self.search_path_entry.insert(0, folder_path)


if __name__ == "__main__":
    # 首先验证授权
    validate_key()

    # 验证通过后启动主程序
    root = tk.Tk()
    app = ModernJsonParserApp(root)
    root.mainloop()
